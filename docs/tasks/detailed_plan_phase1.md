Assumptions:

* Team size: 6 members (mix of FE, BE, AI/ML, Full Stack)
* Capacity per dev per week: \~32 hours (accounting for meetings, testing, review)
* Total capacity per sprint (Phase 1): 6 devs × 32 hrs = 192 hrs/sprint
* Goal for Phase 1: Implement at least 50% of core functionality (frontend + backend + agent flow MVP)

—

# 📌 Phase 1: 3 Weeks (3 Sprints) – MVP

Goal: Build working pipeline from input → skill sync → roadmap → report (first version)

### 🚀 Sprint 1 (Week 1): Core Input + Skill Sync Flow

| Task                                       | Est. Hours | Assigned To    | PIC        | Status      | Step in Pipeline        |
| ------------------------------------------ | ---------- | -------------- | ---------- | ----------- | ----------------------- |
| Setup Frontend scaffolding                 | 3          | Frontend Dev   | DaiNQ11    | Not Started | Initial Setup           |
| Setup Backend scaffolding                  | 3          | Backend Dev    | NamNH46    | Not Started | Initial Setup           |
| Setup AI Service scaffolding               | 3          | AI/ML Dev      | QuyetDB    | Not Started | Initial Setup           |
| Setup CI/CD Pipeline                       | 2          | DevOps         | TrungDD22  | Not Started | Initial Setup           |
| SSO Integration with FSOFT (mocked OK)     | 8          | Backend Dev    | NamNH46    | Not Started | User Authentication     |
| Create Learning Request Form (FE)          | 10         | Frontend Dev   | DaiNQ11    | Not Started | Input Collection        |
| Backend API: Submit Learning Request       | 10         | Backend Dev    | NamNH46    | Not Started | Input Collection        |
| Skill Sync Engine: Connect to mock data    | 16         | Backend Dev    | PhongTN    | Not Started | Skill Profile Fetching  |
| Skill Sync Scheduler (weekly/manual)       | 8          | Backend Dev    | PhongTN    | Not Started | Skill Profile Fetching  |
| Skill Profile Viewer (FE)                  | 10         | Frontend Dev   | DaiNQ11    | Not Started | Skill Profile Display   |
| DB Models: Learning Request, Skill Profile | 6          | Full Stack Dev | NamNH46    | Not Started | Data Persistence        |
| Notifications (mock trigger)               | 6          | Full Stack Dev | TruongPH   | Not Started | User Communication      |
| Unit test coverage                         | 8          | All (rotated)  | All        | Not Started | Quality Assurance       |

🕒 Sprint Total: \~92 hrs FE, \~88 hrs BE = 180 hrs

---

### 🚀 Sprint 2 (Week 2): Gap Analysis + Roadmap Generation (Basic)

| Task                                             | Est. Hours | Assigned To      | PIC        | Status      | Step in Pipeline     |
| ------------------------------------------------ | ---------- | ---------------- | ---------- | ----------- | -------------------- |
| Build Gap Analysis Engine (logic + UI)           | 16         | Backend + AI Dev | QuyetDB    | Not Started | Gap Analysis         |
| Build RAG-based Target Profile Parser            | 12         | AI/ML Dev        | PhongTN    | Not Started | Target Profile Gen   |
| Store & view Gap Results                         | 6          | Backend + FE     | NamNH46    | Not Started | Gap Analysis Display |
| Learning Roadmap Generator (initial AI prompt)   | 16         | AI Dev + Backend | QuyetDB    | Not Started | Roadmap Generation   |
| Display Roadmap Viewer (timeline basic)          | 12         | Frontend Dev     | DaiNQ11    | Not Started | Roadmap Display      |
| DB Models: Gap Analysis, Target Profile, Roadmap | 6          | Backend Dev      | NamNH46    | Not Started | Data Persistence     |
| Agent Interaction Log (basic)                    | 8          | Backend Dev      | NamNH46    | Not Started | Interaction Tracking |
| API: Fetch Gap/Roadmap                           | 6          | Backend Dev      | NamNH46    | Not Started | API Development      |
| Unit + Integration Testing                       | 8          | All              | All        | Not Started | Quality Assurance    |

🕒 Sprint Total: \~90 hrs AI/BE, \~76 hrs FE = 166 hrs

---

### 🚀 Sprint 3 (Week 3): Report Generator + Edits + Export

| Task                                    | Est. Hours | Assigned To    |
| --------------------------------------- | ---------- | -------------- |
| Agent Report Generator (Markdown)       | 10         | AI Dev         |
| Report Viewer (FE)                      | 8          | Frontend Dev   |
| Export to PDF                           | 6          | Full Stack Dev |
| Agent Report Editor (Manual edits)      | 10         | Full Stack Dev |
| Roadmap Edit UI                         | 10         | Frontend Dev   |
| Sync Skill Change Trigger → New Version | 6          | Backend Dev    |
| Notification for roadmap version        | 4          | Backend Dev    |
| Interaction Log Viewer                  | 6          | Frontend Dev   |
| Final QA & Bug Fix Round                | 8          | All            |

🕒 Sprint Total: \~66 hrs FE, \~52 hrs BE = 118 hrs