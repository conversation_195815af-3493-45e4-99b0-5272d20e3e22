#!/usr/bin/env python3
"""
Startup script for the service that handles potential import and configuration issues.
"""
import os
import sys
import time

# Fix OpenTelemetry context loading issues BEFORE any other imports
os.environ['OTEL_PYTHON_CONTEXT'] = 'contextvars_context'
os.environ['OTEL_PYTHON_DISABLED'] = 'true'

# Disable OpenTelemetry auto-instrumentation completely
os.environ['OTEL_SDK_DISABLED'] = 'true'
os.environ['OTEL_PYTHON_LOGGING_AUTO_INSTRUMENTATION_ENABLED'] = 'false'

# Disable <PERSON><PERSON>mith tracing which is causing the OpenTelemetry import issues
os.environ['LANGCHAIN_TRACING_V2'] = 'false'
os.environ['LANGSMITH_TRACING'] = 'false'

import logging

# Configure logging
logging.basicConfig(
    level=logging.INFO,
    format='%(asctime)s - %(name)s - %(levelname)s - %(message)s'
)
logger = logging.getLogger(__name__)

def check_environment():
    """Check if required environment variables are set."""
    logger.info("Checking environment configuration...")

    # Check if at least one LLM provider is configured
    providers = [
        'OPENAI_API_KEY',
        'ANTHROPIC_API_KEY',
        'GOOGLE_API_KEY',
        'GROQ_API_KEY',
        'USE_FAKE_MODEL',
        'OPENROUTER_API_KEY'
    ]

    configured_providers = []
    for provider in providers:
        if os.getenv(provider):
            if provider == 'USE_FAKE_MODEL' and os.getenv(provider).lower() == 'true':
                configured_providers.append('FAKE_MODEL')
            elif provider != 'USE_FAKE_MODEL':
                configured_providers.append(provider)

    if not configured_providers:
        logger.warning("No LLM providers configured. Setting USE_FAKE_MODEL=true as fallback.")
        os.environ['USE_FAKE_MODEL'] = 'true'
    else:
        logger.info(f"Configured providers: {configured_providers}")

    # Set default database configuration if not set
    if not os.getenv('DATABASE_TYPE'):
        os.environ['DATABASE_TYPE'] = 'sqlite'
        logger.info("Set DATABASE_TYPE to sqlite (default)")

    if not os.getenv('SQLITE_DB_PATH'):
        os.environ['SQLITE_DB_PATH'] = '/app/checkpoints.db'
        logger.info("Set SQLITE_DB_PATH to /app/checkpoints.db (default)")



def start_service():
    """Start the main service."""
    logger.info("Starting the service...")

    try:
        # Import and run the main service
        import asyncio
        import uvicorn
        from dotenv import load_dotenv

        # Load environment variables
        load_dotenv()

        # Import settings after environment is configured
        from core import settings

        logger.info(f"Service will start on {settings.HOST}:{settings.PORT}")

        # Set the compatible event loop policy on Windows Systems
        if sys.platform == "win32":
            asyncio.set_event_loop_policy(asyncio.WindowsSelectorEventLoopPolicy())

        # Import the app after all environment setup is complete
        logger.info("Importing service app...")
        from service import app

        # Start the service with the imported app
        uvicorn.run(
            app,
            host=settings.HOST,
            port=settings.PORT,
            reload=settings.is_dev()
        )

    except Exception as e:
        logger.error(f"Failed to start service: {e}")
        logger.exception("Full traceback:")
        sys.exit(1)



def main():
    """Main startup function."""
    logger.info("=== Starting CodePlus Platform Service ===")

    # Wait a moment for container to fully initialize
    time.sleep(1)

    try:
        # Check and configure environment
        check_environment()

        # Start the service
        start_service()

    except KeyboardInterrupt:
        logger.info("Service stopped by user")
        sys.exit(0)
    except Exception as e:
        logger.error(f"Startup failed: {e}")
        logger.exception("Full traceback:")
        sys.exit(1)

if __name__ == "__main__":
    main()
